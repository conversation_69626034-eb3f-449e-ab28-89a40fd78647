"""
⚠️ КРИТИЧЕСКИ ВАЖНО: ИСПОЛЬЗУЕТСЯ ТОЛЬКО ПРОЕКТНАЯ ЛОГИКА! ⚠️

Этот скрипт-оркестратор предназначен для массового анализа структуры книг
и выявления аномалий. Вся логика анализа должна основываться на существующих
проектных модулях (парсеры, детекторы, каноническая модель).

СТРОГО ЗАПРЕЩЕНО дублировать логику или реализовывать собственные алгоритмы парсинга.

---

Скрипт использует модульную архитектуру, делегируя задачи компонентам:
- `tools.analysis.registry`: Управление JSON-реестром аномалий.
- `tools.analysis.detector`: Логика определения аномалий.
- `tools.analysis.mode`: Определение режима запуска (полный анализ / ресканирование).
- `tools.utils`: Общие утилиты для работы с архивами и книгами.

Основные возможности:
- Многопоточная обработка ZIP-архивов.
- Два режима работы: полный анализ и ресканирование только аномальных файлов.
- Генерация CSV-отчета (при полном анализе) и JSON-реестра аномалий.
"""

import argparse
import csv
import io
import os
import sys
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from pathlib import Path

# Добавляем корневую директорию проекта в sys.path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Импорт утилит после корректной настройки sys.path
# Импорты проектной логики
from app.processing.canonical_model import CanonicalBook  # noqa: E402
from app.processing.date_extractor import (  # noqa: E402
    extract_best_date,
)
from app.processing.error_handler import QuarantineError  # noqa: E402
from app.processing.parser_dispatcher import ParserDispatcher  # noqa: E402

# Импорты компонентов анализатора
from tools.analysis.detector import AnomalyDetector  # noqa: E402
from tools.analysis.mode import AnalysisMode  # noqa: E402
from tools.analysis.registry import AnomalyPathRegistry  # noqa: E402
from tools.analysis.types import ProcessingStats  # noqa: E402
from tools.utils import (  # noqa: E402
    format_file_path,
    get_canonical_book_from_stream,
    get_fb2_transformer_from_parser,
    print_processing_status,
    process_zip_archive,
)

# =============================================================================
# НАСТРОЙКИ - УКАЖИТЕ ПУТИ К ПАПКАМ ДЛЯ СКАНИРОВАНИЯ
# =============================================================================
SCAN_PATHS = [
    # r"/mnt/d/Project/books/zip/zip_flibusta",
    # r"/mnt/d/Project/books/zip/zip_searchfloor",
    # r"/mnt/d/Project/books/zip/download/811194-815075",
    # r"/mnt/d/Project/books/zip/download/815076-818518",
    # r"/mnt/d/Project/books/zip/download/818519-822260",
    # r"/mnt/d/Project/books/zip/download/822261-826220",
    # r"/mnt/d/Project/books/zip/download/826221-829529",
    # r"/mnt/d/Project/books/zip/download/829530-832481",
    #
    r"/mnt/d/Project/books/zip/download/600962-603842",
]

# Лимит архивов для обработки (None = все архивы)
ARCHIVES_LIMIT = None  # Для массовой обработки убираем лимиты

# Количество одновременных потоков для обработки архивов
MAX_WORKERS = 20

# Показывать ли детальные ошибки парсинга (False = только краткие сообщения)
SHOW_PARSING_ERRORS = True

# =============================================================================
# НАСТРОЙКИ СИСТЕМЫ ВЫЯВЛЕНИЯ АНОМАЛИЙ
# =============================================================================

# Файл реестра аномальных файлов
ANOMALY_REGISTRY_FILE = Path(__file__).parent / "anomalies_registry.json"

# Принудительный полный анализ (игнорирует существующий реестр)
FORCE_FULL_ANALYSIS = False

# Включение детекции аномалий
ANOMALY_DETECTION_ENABLED = True

# Режим отладки (включается аргументом --debug)
DEBUG_MODE = False

# =============================================================================
# НАСТРОЙКИ ОТЧЕТА - ВЫБЕРИТЕ ЧТО АНАЛИЗИРОВАТЬ
# =============================================================================

# Колонки для CSV отчета (порядок имеет значение!)
REPORT_COLUMNS = [
    "archive_path",  # Путь к архиву
    "fb2_filename",  # Имя FB2 файла
    "title",  # Название книги
    "chapters_count",  # Количество глав
    "sequences_count",  # Количество серий
    "authors_count",  # Количество авторов
    "lang",  # Язык книги
    "is_trial_fragment",  # Ознакомительный отрывок (1/0) - устаревшее
    "date_source",  # Источник даты для book_id
    "generation_date",  # Дата генерации book_id (только дата)
    # === НОВЫЕ КОЛОНКИ ДЛЯ СИСТЕМЫ КАРАНТИНА ===
    "is_small_book",  # Маленькая книга (1/0)
    "is_anthology",  # Антология/сборник (1/0)
    "is_fragment",  # Фрагмент по новому детектору (1/0)
    "anomaly_types",  # Список всех обнаруженных аномалий
    # "content_size_kb",  # Размер контента в КБ (отключен)
    # "genres",          # Жанры (раскомментировать если нужно)
    # "has_annotation",  # Есть ли аннотация (раскомментировать если нужно)
]

# Имя файла отчета (создается в папке tools/)
REPORT_FILE = Path(__file__).parent / "book_analysis_report.csv"

# =============================================================================


def extract_book_data(
    canonical_book: CanonicalBook,
    archive_path: str,
    fb2_filename: str,
    is_fragment: bool = False,
    is_small_book: bool = False,
    is_anthology: bool = False,
    is_fragment_new: bool = False,
    anomaly_types: list[str] = None,
) -> dict:
    """Извлекает данные из книги для отчета."""
    # content_size = sum(len(ch.content_md) for ch in canonical_book.chapters)  # Больше не нужен

    # Формируем строку авторов
    authors_list = []
    for author in canonical_book.authors:
        name_parts = [author.first_name, author.middle_name, author.last_name]
        full_name = " ".join(filter(None, name_parts))
        if full_name:
            authors_list.append(full_name)
    authors_str = "; ".join(authors_list) if authors_list else "Unknown"

    # Формируем строку серий
    sequences_list = []
    for seq in canonical_book.sequences:
        seq_str = seq.name
        if seq.number is not None:
            seq_str += f" #{seq.number}"
        sequences_list.append(seq_str)
    sequences_str = "; ".join(sequences_list) if sequences_list else ""

    # Извлекаем дату генерации и источник даты из raw_source_model
    date_source = "unknown"
    generation_date = "unknown"

    if canonical_book.raw_source_model is not None:
        try:
            # Извлекаем лучшую дату и источник (путь к файлу не передаем)
            best_date, date_source = extract_best_date(canonical_book.raw_source_model, None)
            generation_date = best_date.strftime("%Y-%m-%d")

        except Exception:
            # В случае ошибки остаются значения "unknown"
            pass

    return {
        "archive_path": archive_path,
        "fb2_filename": fb2_filename,
        "title": canonical_book.title or "No Title",
        "chapters_count": len(canonical_book.chapters),
        "sequences_count": len(canonical_book.sequences),
        "authors_count": len(canonical_book.authors),
        "authors": authors_str,
        "sequences": sequences_str,
        "lang": canonical_book.lang or "unknown",
        "is_trial_fragment": 1 if is_fragment else 0,  # Устаревшее поле
        "date_source": date_source,
        "generation_date": generation_date,
        # === НОВЫЕ ПОЛЯ ДЛЯ СИСТЕМЫ КАРАНТИНА ===
        "is_small_book": 1 if is_small_book else 0,
        "is_anthology": 1 if is_anthology else 0,
        "is_fragment": 1 if is_fragment_new else 0,
        "anomaly_types": "; ".join(anomaly_types) if anomaly_types else "",
        # "content_size_kb": round(content_size / 1024, 1),  # Отключен
        "genres": "; ".join(canonical_book.genres) if canonical_book.genres else "",
        "has_annotation": "Yes" if canonical_book.annotation_md.strip() else "No",
    }


class MassBookAnalyzer:
    """Высокопроизводительный анализатор для массовой обработки с системой выявления аномалий."""

    def __init__(self):
        self.parser_dispatcher = ParserDispatcher()
        self.stats = ProcessingStats()

        # Система выявления аномалий
        self.anomaly_registry = AnomalyPathRegistry(ANOMALY_REGISTRY_FILE)
        self.anomaly_detector = AnomalyDetector(self.anomaly_registry)

        # Определяем режим работы
        self.analysis_mode = AnalysisMode.determine_mode(ANOMALY_REGISTRY_FILE, FORCE_FULL_ANALYSIS)

        print(f"🔍 Режим работы: {self.analysis_mode}")

        # Инициализация в зависимости от режима
        if self.analysis_mode == "full_analysis":
            self._init_full_analysis()
        else:
            self._init_anomaly_rescan()

        # Блокировки для потокобезопасности
        self.csv_lock = threading.Lock()
        self.stats_lock = threading.Lock()
        self.print_lock = threading.Lock()

    def _init_full_analysis(self):
        """Инициализация для полного анализа."""
        # ВАЖНО: Сначала загружаем существующий реестр, чтобы сохранить excluded_files
        self.anomaly_registry.load_registry()

        # Создаем/очищаем реестр аномалий (сохраняя excluded_files)
        self.anomaly_registry.clear_and_rebuild()

        # Создаем CSV файл и записываем заголовок
        self.csv_file = open(REPORT_FILE, "w", encoding="utf-8-sig", newline="")
        self.csv_writer = csv.writer(self.csv_file, delimiter=";")
        self.csv_writer.writerow(REPORT_COLUMNS)
        self.csv_file.flush()

        print(f"📄 CSV отчет: {REPORT_FILE}")
        print(f"📋 JSON реестр: {ANOMALY_REGISTRY_FILE}")

    def _init_anomaly_rescan(self):
        """Инициализация для ресканирования аномалий."""
        # Загружаем существующий реестр
        if not self.anomaly_registry.load_registry():
            print("❌ Не удалось загрузить реестр аномалий, переходим к полному анализу")
            self.analysis_mode = "full_analysis"
            self._init_full_analysis()
            return

        self.anomaly_paths = self.anomaly_registry.get_all_anomaly_paths()
        self.target_archives = self.anomaly_registry.get_unique_archive_paths()

        print(f"🎯 Целевых файлов для ресканирования: {len(self.anomaly_paths)}")
        print(f"📦 Архивов для обработки: {len(self.target_archives)}")

        # В режиме ресканирования CSV файл НЕ создаем
        self.csv_file = None
        self.csv_writer = None

    def __del__(self):
        """Закрываем файл при завершении."""
        if hasattr(self, "csv_file") and self.csv_file:
            self.csv_file.close()

    def _collect_archive_paths(self, paths: list[str], limit: int | None = None) -> list[str]:
        """Собирает список архивов в зависимости от режима работы."""
        if self.analysis_mode == "anomaly_rescan":
            # В режиме ресканирования берем только архивы с аномалиями
            archive_paths = [path for path in self.target_archives if os.path.exists(path)]
            return archive_paths[:limit] if limit else archive_paths

        # Полный анализ - собираем все архивы
        archive_paths = []
        for path in paths:
            if not os.path.exists(path):
                continue

            for root, _dirs, files in os.walk(path):
                for file in files:
                    if file.lower().endswith(".zip"):
                        zip_path = os.path.join(root, file)
                        archive_paths.append(zip_path)

                        if limit and len(archive_paths) >= limit:
                            return archive_paths

        return archive_paths

    def _should_process_file(self, archive_path: str, fb2_filename: str) -> bool:
        """Определяет, нужно ли обрабатывать файл в зависимости от режима."""
        if self.analysis_mode == "full_analysis":
            return True

        # В режиме ресканирования обрабатываем только аномальные файлы
        file_path = f"{archive_path}::{fb2_filename}"
        return file_path in self.anomaly_paths

    def process_archives_in_paths(self, paths: list[str], limit: int | None = None) -> None:
        """Обрабатывает архивы в указанных путях с использованием многопоточности."""
        # Собираем список архивов
        archive_paths = self._collect_archive_paths(paths, limit)

        if not archive_paths:
            print("Архивы для обработки не найдены")
            return

        mode_label = "ПОЛНЫЙ АНАЛИЗ" if self.analysis_mode == "full_analysis" else "РЕСКАНИРОВАНИЕ"
        print(f"📊 {mode_label}: архивов {len(archive_paths)}, потоков: {MAX_WORKERS}")

        # Обрабатываем архивы параллельно
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            # Запускаем задачи
            future_to_path = {
                executor.submit(self._process_single_archive_safe, zip_path): zip_path for zip_path in archive_paths
            }

            # Обрабатываем результаты
            for future in as_completed(future_to_path):
                try:
                    future.result()  # Получаем результат (или исключение)
                except Exception:
                    # Тихо пропускаем архивы с критическими ошибками
                    with self.stats_lock:
                        self.stats.error_count += 1

    def _process_single_archive_safe(self, zip_path: str) -> None:
        """Потокобезопасная версия обработки одного ZIP архива, основанная на tools.utils."""
        # Учет обработанного архива
        with self.stats_lock:
            self.stats.processed_archives += 1

        # Фильтр файлов, чтобы пропустить уже исключённые
        def fb2_filter(fb2_filename: str) -> bool:
            return self._should_process_file(zip_path, fb2_filename)

        # Основной процесс обработки FB2
        def fb2_processor(
            archive_path: str,
            fb2_filename: str,
            fb2_stream: "io.BytesIO",
            file_mtime: float | None,
        ):
            try:
                # Парсим книгу из потока
                parser_dispatcher = ParserDispatcher()
                canonical_book = get_canonical_book_from_stream(fb2_stream, fb2_filename, file_mtime, parser_dispatcher)
                fb2_transformer = get_fb2_transformer_from_parser(parser_dispatcher)

                # Детекторы
                is_fragment = self.anomaly_detector.fragment_detector.is_fragment(canonical_book)
                status = "Фрагмент" if is_fragment else "Полная"

                # Дополнительные проверки
                quarantine_type = self.anomaly_detector.small_book_detector.check_book_structure(canonical_book)
                if quarantine_type and not canonical_book.chapters:
                    status += " (без глав)"

                # Аномалии - определяем ОДИН раз
                current_anomalies: set[str] = set()
                if ANOMALY_DETECTION_ENABLED:
                    current_anomalies = set(
                        self.anomaly_detector.detect_anomaly_types(
                            canonical_book,
                            archive_path,
                            fb2_filename,
                            fb2_transformer,
                        )
                    )

                # Лог вывода
                file_path_str = format_file_path(archive_path, fb2_filename)
                if DEBUG_MODE and current_anomalies:
                    reasons = self.anomaly_detector.detect_anomaly_types_with_reasons(
                        canonical_book, archive_path, fb2_filename, fb2_transformer
                    )
                    details = [f"{a} ({reasons.get(a, 'нет причины')})" for a in current_anomalies]
                    print_processing_status(file_path_str, status, details)
                else:
                    print_processing_status(file_path_str, status, list(current_anomalies))

                # Обработка режима
                if self.analysis_mode == "full_analysis":
                    self._process_full_analysis(
                        canonical_book,
                        archive_path,
                        fb2_filename,
                        is_fragment,
                        current_anomalies,
                    )
                else:
                    self._process_anomaly_rescan(
                        archive_path,
                        fb2_filename,
                        current_anomalies,
                    )

                # Статистика
                with self.stats_lock:
                    self.stats.processed_books += 1

            except QuarantineError:
                # Файл в карантине – тихо пропускаем
                return
            except Exception:
                # Любые другие ошибки парсинга
                with self.stats_lock:
                    self.stats.error_count += 1
                if SHOW_PARSING_ERRORS:
                    import traceback

                    print(f"⚠️  Ошибка парсинга {archive_path}::{fb2_filename}\n{traceback.format_exc()}")

        # Общий обработчик ошибок ZIP архива
        def archive_error_handler(archive_path: str, _fb2_filename: str, exc: Exception):
            with self.stats_lock:
                self.stats.error_count += 1
            if SHOW_PARSING_ERRORS:
                print(f"⚠️  Ошибка архива {archive_path}: {exc}")

        # Запускаем обработку архива через util-функцию
        process_zip_archive(
            zip_path=zip_path,
            fb2_processor=fb2_processor,
            fb2_filter=fb2_filter,
            error_handler=archive_error_handler,
        )

    def _process_full_analysis(
        self,
        canonical_book: CanonicalBook,
        archive_path: str,
        fb2_filename: str,
        is_fragment: bool,
        anomaly_types: set[str],
    ):
        """Обработка файла в режиме полного анализа с интегрированными детекторами карантина."""
        # Детекция аномалий уже выполнена, используем переданный `anomaly_types`

        # Получаем результаты детекторов карантина для CSV из набора аномалий
        is_small_book = "small_content" in anomaly_types or "few_chapters" in anomaly_types
        is_anthology = "anthology_books" in anomaly_types
        is_fragment_new = "trial_fragments" in anomaly_types

        # Извлекаем данные для CSV с новыми полями
        book_data = extract_book_data(
            canonical_book,
            archive_path,
            fb2_filename,
            is_fragment,  # Устаревшее поле (совместимость)
            is_small_book,
            is_anthology,
            is_fragment_new,
            list(anomaly_types),
        )

        # Записываем в CSV (потокобезопасно)
        if self.csv_writer:
            with self.csv_lock:
                row = [book_data.get(col, "") for col in REPORT_COLUMNS]
                self.csv_writer.writerow(row)
                self.csv_file.flush()

        # Добавляем аномалии в реестр
        if ANOMALY_DETECTION_ENABLED and anomaly_types:
            with self.stats_lock:
                # В полном анализе каждая аномалия - новая
                self.stats.anomalies_found += len(anomaly_types)

            for anomaly_type in anomaly_types:
                self.anomaly_registry.add_anomaly_path(anomaly_type, archive_path, fb2_filename)

    def _process_anomaly_rescan(
        self,
        archive_path: str,
        fb2_filename: str,
        current_anomalies: set[str],
    ):
        """
        Обработка файла в режиме ресканирования аномалий с полной синхронизацией.
        Реализована комплексная проверка всех типов аномалий.
        """
        if not ANOMALY_DETECTION_ENABLED:
            return

        # 1. Актуальные аномалии уже переданы в `current_anomalies`

        file_path = f"{archive_path}::{fb2_filename}"

        # 2. Получаем ВСЕ типы аномалий, где файл был ранее зарегистрирован
        saved_anomalies = self.anomaly_registry.get_anomaly_types_for_file(file_path)

        # Если у файла не было и нет аномалий, ничего не делаем
        if not saved_anomalies and not current_anomalies:
            return

        # 3. Определяем изменения
        anomalies_to_remove = saved_anomalies - current_anomalies
        anomalies_to_add = current_anomalies - saved_anomalies

        # 4. Обновляем реестр и статистику
        if anomalies_to_remove:
            for anomaly_type in anomalies_to_remove:
                self.anomaly_registry.remove_anomaly_path(anomaly_type, archive_path, fb2_filename)
            with self.stats_lock:
                self.stats.anomalies_fixed += len(anomalies_to_remove)

        if anomalies_to_add:
            for anomaly_type in anomalies_to_add:
                self.anomaly_registry.add_anomaly_path(anomaly_type, archive_path, fb2_filename)
            with self.stats_lock:
                self.stats.new_anomalies_in_rescan += len(anomalies_to_add)

        # 5. Обновляем файловую статистику (полностью/частично исправлен)
        with self.stats_lock:
            if anomalies_to_remove and not current_anomalies:
                self.stats.fully_fixed_files += 1
            elif anomalies_to_remove:
                self.stats.partially_fixed_files += 1

    def close_report(self):
        """Закрывает CSV файл и сохраняет реестр аномалий."""
        if hasattr(self, "csv_file") and self.csv_file:
            self.csv_file.close()

        # Сохраняем реестр аномалий
        if ANOMALY_DETECTION_ENABLED:
            if self.analysis_mode == "full_analysis":
                self.anomaly_registry.update_metadata(self.stats.processed_books)

            self.anomaly_registry.save_registry()

        # Вычисляем время работы и скорость
        self.stats.end_time = datetime.now()
        self.stats.total_time_seconds = (self.stats.end_time - self.stats.start_time).total_seconds()

        # Рассчитываем скорость обработки (избегаем деления на ноль)
        if self.stats.total_time_seconds > 0:
            self.stats.books_per_second = self.stats.processed_books / self.stats.total_time_seconds
        else:
            self.stats.books_per_second = 0.0

    def print_final_report(self):
        """Выводит финальный отчет в зависимости от режима."""
        print("=" * 80)

        if self.analysis_mode == "full_analysis":
            print("✅ ПОЛНЫЙ АНАЛИЗ ЗАВЕРШЕН")
            print(
                f"📊 Архивов: {self.stats.processed_archives} | Книг: {self.stats.processed_books} | Ошибок: {self.stats.error_count}"
            )

            if ANOMALY_DETECTION_ENABLED:
                anomaly_counts = self.anomaly_registry.get_anomaly_counts()
                excluded_count = self.anomaly_registry.get_excluded_count()
                print(f"🔍 Аномалий найдено: {self.stats.anomalies_found}")
                for anomaly_type, count in anomaly_counts.items():
                    if count > 0:
                        print(f"   {anomaly_type}: {count}")
                if excluded_count > 0:
                    print(f"🚫 Файлов исключено: {excluded_count}")

            print(f"📄 CSV отчет: {REPORT_FILE}")

        else:
            print("🎯 РЕСКАНИРОВАНИЕ АНОМАЛИЙ ЗАВЕРШЕНО")
            print(
                f"📊 Архивов: {self.stats.processed_archives} | Файлов: {self.stats.processed_books} | Ошибок: {self.stats.error_count}"
            )

            if ANOMALY_DETECTION_ENABLED:
                print(f"✅ Исправлено аномалий (типов): {self.stats.anomalies_fixed}")
                print(f"  - Полностью исправлено файлов: {self.stats.fully_fixed_files}")
                print(f"  - Частично исправлено файлов: {self.stats.partially_fixed_files}")
                print(f"➕ Обнаружено новых аномалий: {self.stats.new_anomalies_in_rescan}")

                final_counts = self.anomaly_registry.get_anomaly_counts()
                excluded_count = self.anomaly_registry.get_excluded_count()
                total_remaining = sum(final_counts.values())
                print(f"⚠️  Осталось аномалий (типов): {total_remaining}")
                for anomaly_type, count in final_counts.items():
                    if count > 0:
                        print(f"   {anomaly_type}: {count}")
                if excluded_count > 0:
                    print(f"🚫 Файлов исключено: {excluded_count}")

        if ANOMALY_DETECTION_ENABLED:
            print(f"📋 JSON реестр: {ANOMALY_REGISTRY_FILE}")

        print(f"⏱️  Время: {self.stats.total_time_seconds:.1f} сек ({self.stats.total_time_seconds / 60:.1f} мин)")
        print(f"🚀 Скорость: {self.stats.books_per_second:.2f} книг/сек")


def main():
    """Главная функция массовой обработки с системой выявления аномалий."""
    global DEBUG_MODE

    # Парсинг аргументов командной строки
    parser = argparse.ArgumentParser(description="Анализатор структуры книг с системой выявления аномалий")
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Включить отладочный режим с детальными причинами аномалий",
    )

    args = parser.parse_args()
    DEBUG_MODE = args.debug

    if not SCAN_PATHS:
        print("ERROR: Не указаны пути для сканирования в SCAN_PATHS")
        sys.exit(1)

    # Настройка логгеров для подавления детальных ошибок
    if not SHOW_PARSING_ERRORS:
        import logging

        # Подавляем подробные ошибки парсинга
        logging.getLogger("app.processing.parser_dispatcher").setLevel(logging.CRITICAL)
        logging.getLogger("app.processing.date_extractor").setLevel(logging.CRITICAL)
        logging.getLogger("app.processing.parsers.fb2").setLevel(logging.CRITICAL)
        # Подавляем сообщения от FragmentDetector
        logging.getLogger("app.processing.fragment_detector").setLevel(logging.CRITICAL)

    print("🔍 АНАЛИЗАТОР СТРУКТУРЫ КНИГ + СИСТЕМА ВЫЯВЛЕНИЯ АНОМАЛИЙ")
    print(f"📁 Папок сканирования: {len(SCAN_PATHS)} | Потоков: {MAX_WORKERS}")
    print(f"🎯 Детекция аномалий: {'Включена' if ANOMALY_DETECTION_ENABLED else 'Выключена'}")
    print(f"🐞 Детальные ошибки: {'Да' if SHOW_PARSING_ERRORS else 'Нет'}")
    print(f"🔧 Debug режим: {'Включен' if DEBUG_MODE else 'Выключен'}")

    if FORCE_FULL_ANALYSIS:
        print("🔄 ПРИНУДИТЕЛЬНЫЙ ПОЛНЫЙ АНАЛИЗ")

    print("=" * 80)

    analyzer = MassBookAnalyzer()

    try:
        # Обрабатываем архивы
        analyzer.process_archives_in_paths(SCAN_PATHS, ARCHIVES_LIMIT)

        # Закрываем отчет
        analyzer.close_report()

        # Итоговая статистика
        analyzer.print_final_report()

    except KeyboardInterrupt:
        print("\n⏹️  ОБРАБОТКА ПРЕРВАНА")
        # Закрываем файл при прерывании
        analyzer.close_report()
        print(f"⏱️  Частично обработано за {analyzer.stats.total_time_seconds:.1f} сек")
        print(f"📊 Архивов: {analyzer.stats.processed_archives} | Книг: {analyzer.stats.processed_books}")

    except Exception as e:
        print(f"❌ КРИТИЧЕСКАЯ ОШИБКА: {e}")
        analyzer.close_report()
        print(f"⏱️  Частично обработано за {analyzer.stats.total_time_seconds:.1f} сек")
        print(f"📊 Архивов: {analyzer.stats.processed_archives} | Книг: {analyzer.stats.processed_books}")
        sys.exit(1)


if __name__ == "__main__":
    main()
